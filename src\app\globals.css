

/* Import Quicksand font */
@import url('https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap');

:root {
  --font-nevermind-bold: "NeverMindBold", sans-serif;
  --font-nevermind-light: "NeverMindLight", sans-serif;
  --font-poppins: "PoppinsFont", sans-serif;
  --font-nevermind-medium: "NeverMindMedium", sans-serif;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: 'Quicksand', var(--font-nevermind-bold), sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.noLinkStyle {
  text-decoration: none;
  color: inherit;
}

.noScroll {
  overflow: hidden;
}

/* Base font size for rem calculations */
:root {
  font-size: 16px;
}

/* Responsive adjustments for smaller screens */
@media screen and (max-width: 768px) {
  :root {
    font-size: 14px;
  }
}

@media screen and (max-width: 480px) {
  :root {
    font-size: 12px;
  }
}

/* Video player global styles for IRIS and RekindleMinds pages */
:root {
  --plyr-control-icon-size: 24px;
  --plyr-color-main: #f76000;
  --plyr-range-fill-background: #00c6a3;
  --plyr-range-thumb-background: #f76000;
  --plyr-range-thumb-height: 20px;
  --plyr-range-thumb-shadow: 0 1px 1px rgba(215, 26, 18, 0.15),
    0 0 0 1px rgba(215, 26, 18, 0.2);
  --plyr-range-track-height: 08px;
}

/* Hide video player controls */
.plyr__controls .plyr__controls__item:first-child,
.plyr--pip-supported [data-plyr="pip"],
.plyr--fullscreen-enabled [data-plyr="fullscreen"] {
  display: none;
}
